const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const startBtn = document.getElementById('startBtn');
const resetBtn = document.getElementById('resetBtn');

// 游戏参数
const gridSize = 20;
const tileCount = canvas.width / gridSize;
let score = 0;

// 蛇的初始状态
let snake = [
    { x: 10, y: 10 }  // 蛇头起始位置
];
let dx = 0;  // x方向移动
let dy = 0;  // y方向移动

// 食物位置
let food = {
    x: Math.floor(Math.random() * tileCount),
    y: Math.floor(Math.random() * tileCount)
};

// 游戏状态
let isPlaying = false;
let gameSpeed = 150;  // 毫秒
let gameInterval;

// 绘制游戏元素
function draw() {
    // 清空画布
    ctx.fillStyle = '#ecf0f1';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制食物
    ctx.fillStyle = '#e74c3c';
    ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);

    // 绘制蛇
    snake.forEach((segment, index) => {
        ctx.fillStyle = index === 0 ? '#2ecc71' : '#27ae60';  // 蛇头与蛇身不同颜色
        ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
    });
}

// 更新游戏状态
function update() {
    // 移动蛇
    const head = { x: snake[0].x + dx, y: snake[0].y + dy };
    snake.unshift(head);

    // 检查是否吃到食物
    if (head.x === food.x && head.y === food.y) {
        score += 10;
        scoreElement.textContent = score;
        // 生成新食物
        food = {
            x: Math.floor(Math.random() * tileCount),
            y: Math.floor(Math.random() * tileCount)
        };
        // 每得50分增加速度
        if (score % 50 === 0 && gameSpeed > 50) {
            gameSpeed -= 10;
            clearInterval(gameInterval);
            gameInterval = setInterval(gameLoop, gameSpeed);
        }
    } else {
        snake.pop(); // 没吃到食物则移除尾部
    }

    // 检查碰撞
    if (
        head.x < 0 || 
        head.y < 0 || 
        head.x >= tileCount || 
        head.y >= tileCount ||
        snake.slice(1).some(segment => segment.x === head.x && segment.y === head.y)
    ) {
        gameOver();
    }
}

// 游戏循环
function gameLoop() {
    update();
    draw();
}

// 游戏结束
function gameOver() {
    clearInterval(gameInterval);
    isPlaying = false;
    startBtn.textContent = '开始游戏';
    alert(`游戏结束！你的得分是: ${score}`);
}

// 开始游戏
function startGame() {
    if (!isPlaying) {
        // 重置游戏状态
        snake = [{ x: 10, y: 10 }];
        dx = 0;
        dy = 0;
        score = 0;
        scoreElement.textContent = score;
        gameSpeed = 150;
        
        // 生成新食物
        food = {
            x: Math.floor(Math.random() * tileCount),
            y: Math.floor(Math.random() * tileCount)
        };
        
        isPlaying = true;
        startBtn.textContent = '暂停游戏';
        gameInterval = setInterval(gameLoop, gameSpeed);
    } else {
        clearInterval(gameInterval);
        isPlaying = false;
        startBtn.textContent = '继续游戏';
    }
}

// 重置游戏
function resetGame() {
    clearInterval(gameInterval);
    isPlaying = false;
    startBtn.textContent = '开始游戏';
    snake = [{ x: 10, y: 10 }];
    dx = 0;
    dy = 0;
    score = 0;
    scoreElement.textContent = score;
    food = {
        x: Math.floor(Math.random() * tileCount),
        y: Math.floor(Math.random() * tileCount)
    };
    draw();
}

// 键盘控制
function changeDirection(e) {
    // 防止蛇反向移动
    const keyPressed = e.key.toLowerCase();
    const goingUp = dy === -1;
    const goingDown = dy === 1;
    const goingRight = dx === 1;
    const goingLeft = dx === -1;

    if ((keyPressed === 'w' || keyPressed === 'arrowup') && !goingDown) {
        dx = 0;
        dy = -1;
    }
    if ((keyPressed === 's' || keyPressed === 'arrowdown') && !goingUp) {
        dx = 0;
        dy = 1;
    }
    if ((keyPressed === 'a' || keyPressed === 'arrowleft') && !goingRight) {
        dx = -1;
        dy = 0;
    }
    if ((keyPressed === 'd' || keyPressed === 'arrowright') && !goingLeft) {
        dx = 1;
        dy = 0;
    }
}

// 事件监听
startBtn.addEventListener('click', startGame);
resetBtn.addEventListener('click', resetGame);
document.addEventListener('keydown', changeDirection);

// 初始绘制
draw();